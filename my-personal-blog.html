<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeCraft | 前端开发者的个人博客</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#7C3AED',
                        accent: '#EC4899',
                        dark: '#0F172A',
                        light: '#F8FAFC'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                        mono: ['Fira Code', 'monospace']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'typing': 'typing 3.5s steps(40, end)',
                        'blink-caret': 'blinkCaret .75s step-end infinite'
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-20px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(79, 70, 229, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(79, 70, 229, 0.8), 0 0 30px rgba(124, 58, 237, 0.5)' }
                        },
                        typing: {
                            'from': { width: '0' },
                            'to': { width: '100%' }
                        },
                        blinkCaret: {
                            'from, to': { borderColor: 'transparent' },
                            '50%': { borderColor: '#4F46E5' }
                        }
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .bg-gradient-primary {
                background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
            }
            .bg-gradient-secondary {
                background: linear-gradient(135deg, #7C3AED 0%, #EC4899 100%);
            }
            .bg-glass {
                backdrop-filter: blur(12px);
                background-color: rgba(255, 255, 255, 0.1);
            }
            .dark .bg-glass {
                background-color: rgba(15, 23, 42, 0.8);
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .text-gradient {
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .clip-path-slant {
                clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
            }
            .animate-on-scroll {
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.8s ease, transform 0.8s ease;
            }
            .animate-on-scroll.visible {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body class="font-inter bg-light dark:bg-dark text-dark dark:text-light transition-colors duration-500">
    <!-- Preloader -->
    <div id="preloader" class="fixed inset-0 z-50 flex items-center justify-center bg-dark">
        <div class="w-20 h-20 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
    </div>

    <!-- Navigation -->
    <nav id="navbar" class="fixed top-0 left-0 right-0 z-40 transition-all duration-500 py-4">
        <div class="container mx-auto px-4 md:px-8">
            <div class="flex items-center justify-between">
                <a href="#hero" class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-bold text-lg animate-glow">
                        <i class="fa fa-code"></i>
                    </div>
                    <span class="text-xl font-bold tracking-tight">Code<span class="text-primary">Craft</span></span>
                </a>
                
                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#about" class="text-dark dark:text-light hover:text-primary dark:hover:text-primary transition-colors">关于我</a>
                    <a href="#skills" class="text-dark dark:text-light hover:text-primary dark:hover:text-primary transition-colors">技能</a>
                    <a href="#blog" class="text-dark dark:text-light hover:text-primary dark:hover:text-primary transition-colors">博客</a>
                    <a href="#projects" class="text-dark dark:text-light hover:text-primary dark:hover:text-primary transition-colors">项目</a>
                    <a href="#contact" class="px-5 py-2 rounded-full bg-gradient-primary text-white hover:shadow-lg transition-all">联系我</a>
                </div>
                
                <!-- Theme Toggle & Mobile Menu Button -->
                <div class="flex items-center space-x-4">
                    <button id="theme-toggle" class="p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors">
                        <i class="fa fa-moon-o dark:hidden"></i>
                        <i class="fa fa-sun-o hidden dark:inline"></i>
                    </button>
                    
                    <button id="mobile-menu-button" class="md:hidden p-2 rounded-lg text-dark dark:text-light">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-900 shadow-lg rounded-xl mx-4 overflow-hidden bg-glass border border-gray-100 dark:border-gray-800">
            <div class="flex flex-col p-4 space-y-3">
                <a href="#about" class="px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">关于我</a>
                <a href="#skills" class="px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">技能</a>
                <a href="#blog" class="px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">博客</a>
                <a href="#projects" class="px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">项目</a>
                <a href="#contact" class="px-4 py-2 rounded-lg bg-gradient-primary text-white text-center transition-all">联系我</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-primary/5 to-transparent dark:from-primary/10"></div>
        <div class="absolute top-20 right-20 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div class="absolute bottom-20 left-20 w-80 h-80 bg-secondary/10 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 1s;"></div>
        
        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <div class="flex flex-col items-center text-center">
                <!-- Avatar -->
                <div class="relative mb-8">
                    <div class="w-32 h-32 md:w-40 md:h-40 rounded-full bg-gradient-primary p-1 animate-float">
                        <div class="w-full h-full rounded-full bg-white dark:bg-gray-900 flex items-center justify-center">
                            <i class="fa fa-user-circle-o text-6xl md:text-7xl text-primary/80"></i>
                        </div>
                    </div>
                    <div class="absolute -bottom-2 -right-2 w-12 h-12 rounded-full bg-green-500 border-4 border-white dark:border-gray-900 flex items-center justify-center text-white font-bold animate-pulse"></div>
                </div>
                
                <!-- Intro Text -->
                <h1 class="text-4xl md:text-6xl font-bold mb-4 leading-tight">
                    <span>你好，我是</span>
                    <span class="bg-gradient-primary text-gradient text-shadow">张明</span>
                </h1>
                <div class="relative h-12 mb-8">
                    <h2 class="text-xl md:text-3xl font-medium text-gray-600 dark:text-gray-300 relative inline-block overflow-hidden whitespace-nowrap border-r-4 border-primary animate-typing">
                        前端开发工程师 & UI/UX 爱好者
                    </h2>
                </div>
                <p class="text-gray-600 dark:text-gray-300 max-w-2xl mb-8 text-lg">
                    热衷于创建美观、高性能的 Web 应用，追求代码质量与用户体验的完美结合。
                </p>
                
                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-12">
                    <a href="#contact" class="px-8 py-3 rounded-full bg-gradient-primary text-white font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all">
                        联系我 <i class="fa fa-paper-plane ml-2"></i>
                    </a>
                    <a href="#projects" class="px-8 py-3 rounded-full border-2 border-primary text-primary dark:text-primary font-medium hover:bg-primary/10 transition-colors">
                        查看作品 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
                
                <!-- Social Links -->
                <div class="flex space-x-4">
                    <a href="#" class="w-10 h-10 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-all">
                        <i class="fa fa-github"></i>
                    </a>
                    <a href="#" class="w-10 h-10 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-all">
                        <i class="fa fa-linkedin"></i>
                    </a>
                    <a href="#" class="w-10 h-10 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-all">
                        <i class="fa fa-twitter"></i>
                    </a>
                    <a href="#" class="w-10 h-10 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-all">
                        <i class="fa fa-codepen"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Scroll Down Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <a href="#about" class="text-gray-400 hover:text-primary dark:hover:text-primary transition-colors">
                <i class="fa fa-chevron-down text-2xl"></i>
            </a>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white dark:bg-gray-900">
        <div class="container mx-auto px-4 md:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">关于<span class="text-primary">我</span></h2>
                <div class="w-24 h-1 bg-gradient-primary mx-auto"></div>
            </div>
            
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="relative animate-on-scroll">
                    <div class="w-full h-full bg-gradient-secondary rounded-2xl overflow-hidden shadow-xl transform rotate-3"></div>
                    <div class="absolute top-0 left-0 w-full h-full bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-2xl transform -rotate-3">
                        <div class="p-8 h-full flex flex-col justify-center">
                            <h3 class="text-2xl font-bold mb-6">个人简介</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                                我是一名拥有5年前端开发经验的软件工程师，专注于创建用户友好、性能卓越的Web应用。我热爱前端开发，热衷于探索新技术并将其应用到实际项目中。
                            </p>
                            <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                                我相信，优秀的前端不仅要美观，还要注重性能、可访问性和用户体验。我不断学习和成长，努力提升自己的技术水平，以应对日益复杂的Web开发挑战。
                            </p>
                            <div class="flex space-x-4">
                                <a href="#contact" class="text-primary hover:underline">联系我 <i class="fa fa-arrow-right ml-1"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="animate-on-scroll" style="animation-delay: 200ms;">
                    <h3 class="text-2xl font-bold mb-6">我的故事</h3>
                    <div class="space-y-6">
                        <div class="flex">
                            <div class="flex-shrink-0 mr-6">
                                <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                                    <i class="fa fa-graduation-cap text-2xl"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-xl font-semibold mb-2">教育背景</h4>
                                <p class="text-gray-600 dark:text-gray-300 mb-1">计算机科学与技术硕士</p>
                                <p class="text-gray-500 dark:text-gray-400">北京大学 | 2015-2018</p>
                            </div>
                        </div>
                        
                        <div class="flex">
                            <div class="flex-shrink-0 mr-6">
                                <div class="w-16 h-16 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
                                    <i class="fa fa-briefcase text-2xl"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-xl font-semibold mb-2">工作经历</h4>
                                <p class="text-gray-600 dark:text-gray-300 mb-1">高级前端开发工程师</p>
                                <p class="text-gray-500 dark:text-gray-400">科技有限公司 | 2018-至今</p>
                            </div>
                        </div>
                        
                        <div class="flex">
                            <div class="flex-shrink-0 mr-6">
                                <div class="w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center text-accent">
                                    <i class="fa fa-lightbulb-o text-2xl"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-xl font-semibold mb-2">个人理念</h4>
                                <p class="text-gray-600 dark:text-gray-300">
                                    我相信技术的力量可以改变世界，每一行代码都承载着创造美好用户体验的使命。在工作中，我注重代码质量和用户体验，追求技术卓越与设计美学的完美结合。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-gray-50 dark:bg-gray-800">
        <div class="container mx-auto px-4 md:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">我的<span class="text-primary">技能</span></h2>
                <div class="w-24 h-1 bg-gradient-primary mx-auto"></div>
                <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    不断学习和探索前沿技术，提升自己的专业能力
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Technical Skills -->
                <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 animate-on-scroll">
                    <div class="w-14 h-14 rounded-full bg-primary/10 flex items-center justify-center text-primary mb-6">
                        <i class="fa fa-code text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-6">技术技能</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="font-medium">HTML5 & CSS3</span>
                                <span>95%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-primary h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="font-medium">JavaScript / TypeScript</span>
                                <span>90%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-primary h-2 rounded-full" style="width: 90%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="font-medium">React & Vue.js</span>
                                <span>85%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-primary h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="font-medium">Node.js</span>
                                <span>80%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-primary h-2 rounded-full" style="width: 80%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="font-medium">Tailwind CSS</span>
                                <span>92%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-primary h-2 rounded-full" style="width: 92%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Tools & Frameworks -->
                <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 animate-on-scroll" style="animation-delay: 200ms;">
                    <div class="w-14 h-14 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mb-6">
                        <i class="fa fa-wrench text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-6">工具与框架</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg flex items-center space-x-3">
                            <div class="w-8 h-8 rounded bg-blue-500"></div>
                            <span>React</span>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg flex items-center space-x-3">
                            <div class="w-8 h-8 rounded bg-green-500"></div>
                            <span>Vue.js</span>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg flex items-center space-x-3">
                            <div class="w-8 h-8 rounded bg-blue-600"></div>
                            <span>Next.js</span>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg flex items-center space-x-3">
                            <div class="w-8 h-8 rounded bg-purple-500"></div>
                            <span>NestJS</span>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg flex items-center space-x-3">
                            <div class="w-8 h-8 rounded bg-orange-500"></div>
                            <span>Git</span>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg flex items-center space-x-3">
                            <div class="w-8 h-8 rounded bg-gray-700"></div>
                            <span>Webpack</span>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg flex items-center space-x-3">
                            <div class="w-8 h-8 rounded bg-pink-500"></div>
                            <span>Figma</span>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg flex items-center space-x-3">
                            <div class="w-8 h-8 rounded bg-indigo-500"></div>
                            <span>Docker</span>
                        </div>
                    </div>
                </div>
                
                <!-- Soft Skills -->
                <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 animate-on-scroll" style="animation-delay: 400ms;">
                    <div class="w-14 h-14 rounded-full bg-accent/10 flex items-center justify-center text-accent mb-6">
                        <i class="fa fa-users text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-6">软技能</h3>
                    <ul class="space-y-3">
                        <li class="flex items-center space-x-3">
                            <i class="fa fa-check text-green-500"></i>
                            <span>优秀的问题解决能力</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <i class="fa fa-check text-green-500"></i>
                            <span>团队协作与沟通</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <i class="fa fa-check text-green-500"></i>
                            <span>项目管理与时间管理</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <i class="fa fa-check text-green-500"></i>
                            <span>持续学习与适应能力</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <i class="fa fa-check text-green-500"></i>
                            <span>创造性思维</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <i class="fa fa-check text-green-500"></i>
                            <span>用户体验设计思维</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <i class="fa fa-check text-green-500"></i>
                            <span>英语沟通能力</span>
                        </li>
                    </ul>
                    
                    <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h4 class="font-semibold mb-4">语言能力</h4>
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="w-24">中文</span>
                            <div class="flex">
                                <i class="fa fa-star text-yellow-400"></i>
                                <i class="fa fa-star text-yellow-400"></i>
                                <i class="fa fa-star text-yellow-400"></i>
                                <i class="fa fa-star text-yellow-400"></i>
                                <i class="fa fa-star text-yellow-400"></i>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-24">英语</span>
                            <div class="flex">
                                <i class="fa fa-star text-yellow-400"></i>
                                <i class="fa fa-star text-yellow-400"></i>
                                <i class="fa fa-star text-yellow-400"></i>
                                <i class="fa fa-star text-yellow-400"></i>
                                <i class="fa fa-star-half-o text-yellow-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section id="blog" class="py-20 bg-white dark:bg-gray-900">
        <div class="container mx-auto px-4 md:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">我的<span class="text-primary">博客</span></h2>
                <div class="w-24 h-1 bg-gradient-primary mx-auto"></div>
                <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    分享我的技术心得、学习笔记和前端领域的思考
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Blog Card 1 -->
                <article class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-on-scroll group">
                    <div class="h-48 bg-gradient-primary relative overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center opacity-20">
                            <i class="fa fa-code text-8xl text-white"></i>
                        </div>
                        <div class="absolute top-4 left-4">
                            <span class="px-3 py-1 bg-white/90 text-primary text-sm font-medium rounded-full">React</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3 group-hover:text-primary transition-colors">
                            React 18 新特性深度解析：并发渲染与自动批处理
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                            React 18 带来了一系列令人兴奋的新特性，包括并发渲染、自动批处理、Transitions API 等。本文将深入探讨这些新特性的工作原理和实际应用场景。
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <img src="https://picsum.photos/id/1005/40/40" alt="Author" class="w-8 h-8 rounded-full">
                                <span class="text-sm text-gray-500 dark:text-gray-400">张明</span>
                            </div>
                            <span class="text-sm text-gray-500 dark:text-gray-400">2024-03-15</span>
                        </div>
                    </div>
                </article>
                
                <!-- Blog Card 2 -->
                <article class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-on-scroll group" style="animation-delay: 200ms;">
                    <div class="h-48 bg-gradient-secondary relative overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center opacity-20">
                            <i class="fa fa-paint-brush text-8xl text-white"></i>
                        </div>
                        <div class="absolute top-4 left-4">
                            <span class="px-3 py-1 bg-white/90 text-secondary text-sm font-medium rounded-full">设计</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3 group-hover:text-secondary transition-colors">
                            现代 Web 设计趋势 2024：从玻璃拟态到新拟态
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                            2024 年，Web 设计领域出现了许多新趋势。本文将介绍今年最流行的设计风格，包括玻璃拟态、新拟态、Neobrutalism 等，并探讨如何在实际项目中应用这些设计理念。
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <img src="https://picsum.photos/id/1005/40/40" alt="Author" class="w-8 h-8 rounded-full">
                                <span class="text-sm text-gray-500 dark:text-gray-400">张明</span>
                            </div>
                            <span class="text-sm text-gray-500 dark:text-gray-400">2024-02-28</span>
                        </div>
                    </div>
                </article>
                
                <!-- Blog Card 3 -->
                <article class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-on-scroll group" style="animation-delay: 400ms;">
                    <div class="h-48 bg-gradient-to-br from-green-500 to-blue-500 relative overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center opacity-20">
                            <i class="fa fa-bolt text-8xl text-white"></i>
                        </div>
                        <div class="absolute top-4 left-4">
                            <span class="px-3 py-1 bg-white/90 text-green-600 text-sm font-medium rounded-full">性能</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3 group-hover:text-green-600 transition-colors">
                            前端性能优化实战：从 3 秒到 0.5 秒的蜕变
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                            性能优化是前端开发中永恒的话题。本文将分享一个实际项目的性能优化案例，从代码分割、图片优化、缓存策略等多个方面，详细介绍如何将页面加载时间从 3 秒优化到 0.5 秒。
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <img src="https://picsum.photos/id/1005/40/40" alt="Author" class="w-8 h-8 rounded-full">
                                <span class="text-sm text-gray-500 dark:text-gray-400">张明</span>
                            </div>
                            <span class="text-sm text-gray-500 dark:text-gray-400">2024-02-10</span>
                        </div>
                    </div>
                </article>
            </div>
            
            <div class="text-center mt-12">
                <a href="#" class="inline-flex items-center px-6 py-3 rounded-full border-2 border-primary text-primary font-medium hover:bg-primary hover:text-white transition-all duration-300">
                    查看全部文章 <i class="fa fa-long-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-50 dark:bg-gray-800 clip-path-slant">
        <div class="container mx-auto px-4 md:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">精选<span class="text-primary">项目</span></h2>
                <div class="w-24 h-1 bg-gradient-primary mx-auto"></div>
                <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    展示我的技术能力和项目经验
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-12">
                <!-- Project 1 -->
                <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden animate-on-scroll group">
                    <div class="h-64 bg-gradient-to-br from-blue-600 to-indigo-700 relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center">
                                <div class="text-6xl mb-4">🛒</div>
                                <h3 class="text-2xl font-bold text-white">电商平台</h3>
                            </div>
                        </div>
                        <div class="absolute bottom-4 left-4 flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">React</span>
                            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">Node.js</span>
                            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">MongoDB</span>
                        </div>
                        <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <div class="bg-white text-gray-900 px-6 py-3 rounded-full font-medium transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                                查看项目 <i class="fa fa-external-link ml-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">现代化电商平台</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            基于 React 和 Node.js 构建的全栈电商平台，支持用户注册、商品管理、购物车、支付等功能。采用微服务架构，具有良好的扩展性和性能。
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-3">
                                <a href="#" class="text-gray-500 hover:text-primary transition-colors">
                                    <i class="fa fa-github"></i>
                                </a>
                                <a href="#" class="text-gray-500 hover:text-primary transition-colors">
                                    <i class="fa fa-external-link"></i>
                                </a>
                            </div>
                            <span class="text-sm text-gray-500">2023 年</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 2 -->
                <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden animate-on-scroll group" style="animation-delay: 200ms;">
                    <div class="h-64 bg-gradient-to-br from-purple-600 to-pink-600 relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center">
                                <div class="text-6xl mb-4">📊</div>
                                <h3 class="text-2xl font-bold text-white">数据可视化平台</h3>
                            </div>
                        </div>
                        <div class="absolute bottom-4 left-4 flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">Vue.js</span>
                            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">D3.js</span>
                            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full backdrop-blur-sm">WebSocket</span>
                        </div>
                        <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <div class="bg-white text-gray-900 px-6 py-3 rounded-full font-medium transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                                查看项目 <i class="fa fa-external-link ml-2"></i>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">实时数据可视化平台</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            使用 Vue.js 和 D3.js 构建的实时数据可视化平台，支持多种图表类型和实时数据更新。平台具有良好的交互性和用户体验，适用于监控系统和数据分析场景。
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-3">
                                <a href="#" class="text-gray-500 hover:text-primary transition-colors">
                                    <i class="fa fa-github"></i>
                                </a>
                                <a href="#" class="text-gray-500 hover:text-primary transition-colors">
                                    <i class="fa fa-external-link"></i>
                                </a>
                            </div>
                            <span class="text-sm text-gray-500">2023 年</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 bg-white dark:bg-gray-900">
        <div class="container mx-auto px-4 md:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">客户<span class="text-primary">评价</span></h2>
                <div class="w-24 h-1 bg-gradient-primary mx-auto"></div>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 shadow-md animate-on-scroll">
                    <div class="flex items-center space-x-1 text-yellow-400 mb-4">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 mb-6 italic">
                        "张明是一位非常出色的前端开发者，他不仅技术扎实，而且对用户体验有着深刻的理解。他的代码质量高，解决问题的能力强，是团队中不可或缺的人才。"
                    </p>
                    <div class="flex items-center space-x-4">
                        <img src="https://picsum.photos/id/1012/60/60" alt="Client" class="w-12 h-12 rounded-full">
                        <div>
                            <h4 class="font-bold">李总监</h4>
                            <p class="text-sm text-gray-500">产品总监 @ 科技公司</p>
                        </div>
                    </div>
                </div>
                
                <!-- Testimonial 2 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 shadow-md animate-on-scroll" style="animation-delay: 200ms;">
                    <div class="flex items-center space-x-1 text-yellow-400 mb-4">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 mb-6 italic">
                        "与张明合作是一种愉快的体验。他总是能够准确理解需求，并提出创造性的解决方案。他的前端实现不仅美观，而且性能出色，为我们的产品增添了不少价值。"
                    </p>
                    <div class="flex items-center space-x-4">
                        <img src="https://picsum.photos/id/1027/60/60" alt="Client" class="w-12 h-12 rounded-full">
                        <div>
                            <h4 class="font-bold">王经理</h4>
                            <p class="text-sm text-gray-500">项目经理 @ 互联网公司</p>
                        </div>
                    </div>
                </div>
                
                <!-- Testimonial 3 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 shadow-md animate-on-scroll" style="animation-delay: 400ms;">
                    <div class="flex items-center space-x-1 text-yellow-400 mb-4">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star-half-o"></i>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 mb-6 italic">
                        "张明的技术能力令人印象深刻，他能够快速学习新技术并应用到项目中。他的沟通能力也很好，能够与团队成员和客户保持良好的沟通，确保项目顺利进行。"
                    </p>
                    <div class="flex items-center space-x-4">
                        <img src="https://picsum.photos/id/1025/60/60" alt="Client" class="w-12 h-12 rounded-full">
                        <div>
                            <h4 class="font-bold">赵设计师</h4>
                            <p class="text-sm text-gray-500">UI/UX 设计师 @ 设计公司</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50 dark:bg-gray-800">
        <div class="container mx-auto px-4 md:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">联系<span class="text-primary">我</span></h2>
                <div class="w-24 h-1 bg-gradient-primary mx-auto"></div>
                <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    有任何问题或合作意向？请随时联系我，我会尽快回复您
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="animate-on-scroll">
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8">
                        <h3 class="text-2xl font-bold mb-6">发送消息</h3>
                        <form class="space-y-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">姓名</label>
                                <input type="text" id="name" class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary" placeholder="请输入您的姓名">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">邮箱</label>
                                <input type="email" id="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary" placeholder="请输入您的邮箱">
                            </div>
                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">主题</label>
                                <input type="text" id="subject" class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary" placeholder="请输入消息主题">
                            </div>
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">消息内容</label>
                                <textarea id="message" rows="5" class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary" placeholder="请输入您的消息"></textarea>
                            </div>
                            <button type="submit" class="w-full px-6 py-3 rounded-lg bg-gradient-primary text-white font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all">
                                发送消息 <i class="fa fa-paper-plane ml-2"></i>
                            </button>
                        </form>
                    </div>
                </div>
                
                <div class="animate-on-scroll" style="animation-delay: 200ms;">
                    <div class="space-y-8">
                        <h3 class="text-2xl font-bold">联系方式</h3>
                        
                        <div class="flex items-center space-x-6">
                            <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl">
                                <i class="fa fa-envelope-o"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-semibold mb-1">邮箱</h4>
                                <p class="text-gray-600 dark:text-gray-300"><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-6">
                            <div class="w-16 h-16 rounded-full bg-secondary/10 flex items-center justify-center text-secondary text-2xl">
                                <i class="fa fa-phone"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-semibold mb-1">电话</h4>
                                <p class="text-gray-600 dark:text-gray-300">+86 138 0000 0000</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-6">
                            <div class="w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center text-accent text-2xl">
                                <i class="fa fa-map-marker"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-semibold mb-1">地址</h4>
                                <p class="text-gray-600 dark:text-gray-300">北京市海淀区中关村科技园区</p>
                            </div>
                        </div>
                        
                        <div class="pt-8">
                            <h4 class="text-lg font-semibold mb-4">关注我</h4>
                            <div class="flex space-x-4">
                                <a href="#" class="w-12 h-12 rounded-full bg-white dark:bg-gray-900 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-all">
                                    <i class="fa fa-github text-xl"></i>
                                </a>
                                <a href="#" class="w-12 h-12 rounded-full bg-white dark:bg-gray-900 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-all">
                                    <i class="fa fa-linkedin text-xl"></i>
                                </a>
                                <a href="#" class="w-12 h-12 rounded-full bg-white dark:bg-gray-900 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-all">
                                    <i class="fa fa-twitter text-xl"></i>
                                </a>
                                <a href="#" class="w-12 h-12 rounded-full bg-white dark:bg-gray-900 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition-all">
                                    <i class="fa fa-codepen text-xl"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4 md:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-6">
                        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-bold text-lg">
                            <i class="fa fa-code"></i>
                        </div>
                        <span class="text-xl font-bold tracking-tight">Code<span class="text-primary">Craft</span></span>
                    </div>
                    <p class="text-gray-400 mb-6">
                        用代码构建美好世界，用技术改变未来。
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-primary transition-colors">
                            <i class="fa fa-github text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary transition-colors">
                            <i class="fa fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary transition-colors">
                            <i class="fa fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary transition-colors">
                            <i class="fa fa-codepen text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-6">快速链接</h3>
                    <ul class="space-y-3">
                        <li><a href="#about" class="text-gray-400 hover:text-primary transition-colors">关于我</a></li>
                        <li><a href="#skills" class="text-gray-400 hover:text-primary transition-colors">技能</a></li>
                        <li><a href="#blog" class="text-gray-400 hover:text-primary transition-colors">博客</a></li>
                        <li><a href="#projects" class="text-gray-400 hover:text-primary transition-colors">项目</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-primary transition-colors">联系我</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-6">技术栈</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">HTML5 & CSS3</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">JavaScript / TypeScript</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">React & Vue.js</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">Node.js</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">Tailwind CSS</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-6">订阅博客</h3>
                    <p class="text-gray-400 mb-4">
                        订阅我的博客，获取最新的技术文章和更新。
                    </p>
                    <form class="flex">
                        <input type="email" placeholder="您的邮箱地址" class="flex-1 px-4 py-2 rounded-l-lg bg-gray-800 text-white border-0 focus:outline-none focus:ring-2 focus:ring-primary">
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-r-lg hover:bg-primary/90 transition-colors">
                            <i class="fa fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500">
                <p>&copy; 2024 CodeCraft. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Preloader
        window.addEventListener('load', function() {
            setTimeout(function() {
                const preloader = document.getElementById('preloader');
                preloader.style.opacity = '0';
                preloader.style.transition = 'opacity 0.5s ease';
                setTimeout(function() {
                    preloader.style.display = 'none';
                }, 500);
            }, 800);
        });
        
        // Theme Toggle
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;
        
        // Check for saved theme preference or default to system preference
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            html.classList.add('dark');
        } else {
            html.classList.remove('dark');
        }
        
        themeToggle.addEventListener('click', function() {
            html.classList.toggle('dark');
            localStorage.theme = html.classList.contains('dark') ? 'dark' : 'light';
        });
        
        // Mobile Menu
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Close mobile menu when clicking on a link
        const mobileLinks = mobileMenu.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
            });
        });
        
        // Navbar Scroll Effect
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('bg-white/90', 'dark:bg-gray-900/90', 'backdrop-blur-md', 'shadow-md');
                navbar.classList.remove('py-4');
                navbar.classList.add('py-2');
            } else {
                navbar.classList.remove('bg-white/90', 'dark:bg-gray-900/90', 'backdrop-blur-md', 'shadow-md');
                navbar.classList.add('py-4');
                navbar.classList.remove('py-2');
            }
        });
        
        // Smooth Scrolling for Anchor Links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Scroll Animation Observer
        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };
        
        const observer = new IntersectionObserver(function(entries, observer) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        document.querySelectorAll('.animate-on-scroll').forEach(element => {
            observer.observe(element);
        });
        
        // Form Submission Handling
        const contactForm = document.querySelector('#contact form');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // Here you would typically send the form data to a server
                alert('感谢您的留言！我会尽快回复您。');
                contactForm.reset();
            });
        }
    </script>
</body>

</html>