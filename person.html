<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端开发者的个人博客</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'float': 'float 3s ease-in-out infinite',
                        'gradient': 'gradient 8s ease infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                        gradient: {
                            '0%, 100%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 400% 400%;
            animation: gradient 8s ease infinite;
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass-effect">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div
                        class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">F</span>
                    </div>
                    <span class="font-bold text-xl">前端开发者</span>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="hover:text-blue-500 transition-colors">首页</a>
                    <a href="#about" class="hover:text-blue-500 transition-colors">关于我</a>
                    <a href="#blog" class="hover:text-blue-500 transition-colors">博客</a>
                    <a href="#projects" class="hover:text-blue-500 transition-colors">项目</a>
                    <a href="#contact" class="hover:text-blue-500 transition-colors">联系</a>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="theme-toggle"
                        class="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z">
                            </path>
                        </svg>
                    </button>
                    <button class="md:hidden" id="mobile-menu-btn">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="fixed inset-0 z-40 hidden">
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        <div class="fixed right-0 top-0 h-full w-64 glass-effect">
            <div class="flex flex-col p-6 space-y-4">
                <button id="close-menu" class="self-end">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
                <a href="#home" class="text-lg hover:text-blue-500 transition-colors">首页</a>
                <a href="#about" class="text-lg hover:text-blue-500 transition-colors">关于我</a>
                <a href="#blog" class="text-lg hover:text-blue-500 transition-colors">博客</a>
                <a href="#projects" class="text-lg hover:text-blue-500 transition-colors">项目</a>
                <a href="#contact" class="text-lg hover:text-blue-500 transition-colors">联系</a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="absolute inset-0 gradient-bg opacity-10"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-900">
        </div>

        <div class="relative z-10 text-center max-w-4xl mx-auto px-4">
            <div class="animate-fade-in">
                <div
                    class="w-32 h-32 mx-auto mb-8 rounded-full gradient-bg flex items-center justify-center animate-float">
                    <span class="text-white text-4xl font-bold">👨‍💻</span>
                </div>
                <h1
                    class="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    前端开发者
                </h1>
                <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 animate-slide-up">
                    用代码构建美好世界，用技术改变未来
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
                    <a href="#about"
                        class="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                        了解更多
                    </a>
                    <a href="#blog"
                        class="px-8 py-3 border-2 border-blue-500 text-blue-500 dark:text-blue-400 rounded-full hover:bg-blue-500 hover:text-white transition-all duration-300">
                        阅读博客
                    </a>
                </div>
            </div>
        </div>

        <!-- Floating Elements -->
        <div
            class="absolute top-20 left-10 w-20 h-20 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 animate-float">
        </div>
        <div class="absolute bottom-20 right-10 w-16 h-16 bg-purple-200 dark:bg-purple-800 rounded-full opacity-20 animate-float"
            style="animation-delay: 1s;"></div>
        <div class="absolute top-1/2 left-20 w-12 h-12 bg-pink-200 dark:bg-pink-800 rounded-full opacity-20 animate-float"
            style="animation-delay: 2s;"></div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white dark:bg-gray-800">
        <div class="max-w-6xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">关于我</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">热爱技术，追求完美</p>
            </div>

            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="space-y-6">
                    <h3 class="text-2xl font-bold text-blue-600 dark:text-blue-400">技术栈</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-orange-500 rounded"></div>
                                <span class="font-medium">HTML5</span>
                            </div>
                        </div>
                        <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-500 rounded"></div>
                                <span class="font-medium">CSS3</span>
                            </div>
                        </div>
                        <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-yellow-500 rounded"></div>
                                <span class="font-medium">JavaScript</span>
                            </div>
                        </div>
                        <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-600 rounded"></div>
                                <span class="font-medium">React</span>
                            </div>
                        </div>
                        <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-500 rounded"></div>
                                <span class="font-medium">Vue.js</span>
                            </div>
                        </div>
                        <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-purple-500 rounded"></div>
                                <span class="font-medium">Tailwind</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <h3 class="text-2xl font-bold text-blue-600 dark:text-blue-400">个人简介</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                        我是一名充满激情的前端开发者，专注于创建用户友好、性能优异的Web应用。
                        拥有5年以上的前端开发经验，热衷于学习新技术并将其应用到实际项目中。
                    </p>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
                        我相信技术的力量可以改变世界，每一行代码都承载着创造美好用户体验的使命。
                        在工作中，我注重代码质量和用户体验，追求技术卓越与设计美学的完美结合。
                    </p>
                    <div class="flex space-x-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">50+</div>
                            <div class="text-sm text-gray-500">完成项目</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">5+</div>
                            <div class="text-sm text-gray-500">年经验</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600 dark:text-green-400">100%</div>
                            <div class="text-sm text-gray-500">客户满意度</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section id="blog" class="py-20 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-6xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">最新博客</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">分享技术心得与思考</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Blog Card 1 -->
                <article
                    class="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden group">
                    <div class="h-48 bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden">
                        <div
                            class="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300">
                        </div>
                        <div class="absolute top-4 left-4">
                            <span class="px-3 py-1 bg-white bg-opacity-90 text-sm font-medium rounded-full">技术</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3
                            class="text-xl font-bold mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                            React 18 新特性深度解析
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                            深入探讨React 18带来的并发特性、自动批处理等新功能，以及如何在实际项目中应用这些特性。
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">2024年1月15日</span>
                            <button class="text-blue-600 dark:text-blue-400 hover:underline">阅读更多</button>
                        </div>
                    </div>
                </article>

                <!-- Blog Card 2 -->
                <article
                    class="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden group">
                    <div class="h-48 bg-gradient-to-br from-green-400 to-blue-500 relative overflow-hidden">
                        <div
                            class="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300">
                        </div>
                        <div class="absolute top-4 left-4">
                            <span class="px-3 py-1 bg-white bg-opacity-90 text-sm font-medium rounded-full">设计</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3
                            class="text-xl font-bold mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                            现代Web设计趋势分析
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                            探讨2024年Web设计的最新趋势，包括微交互、玻璃拟态、暗色模式等设计理念的实践应用。
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">2024年1月10日</span>
                            <button class="text-blue-600 dark:text-blue-400 hover:underline">阅读更多</button>
                        </div>
                    </div>
                </article>

                <!-- Blog Card 3 -->
                <article
                    class="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden group">
                    <div class="h-48 bg-gradient-to-br from-purple-400 to-pink-500 relative overflow-hidden">
                        <div
                            class="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300">
                        </div>
                        <div class="absolute top-4 left-4">
                            <span class="px-3 py-1 bg-white bg-opacity-90 text-sm font-medium rounded-full">性能</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3
                            class="text-xl font-bold mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                            前端性能优化实战指南
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                            从代码分割到懒加载，从缓存策略到资源优化，全面解析前端性能优化的最佳实践。
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">2024年1月5日</span>
                            <button class="text-blue-600 dark:text-blue-400 hover:underline">阅读更多</button>
                        </div>
                    </div>
                </article>
            </div>

            <div class="text-center mt-12">
                <a href="#"
                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    查看所有文章
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-white dark:bg-gray-800">
        <div class="max-w-6xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">精选项目</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">展示我的技术实力与创造力</p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Project 1 -->
                <div class="group">
                    <div class="relative overflow-hidden rounded-xl shadow-lg">
                        <div
                            class="h-64 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <div class="text-white text-center">
                                <div class="text-6xl mb-4">🚀</div>
                                <h3 class="text-2xl font-bold">电商平台</h3>
                            </div>
                        </div>
                        <div
                            class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <button
                                    class="px-6 py-3 bg-white text-gray-900 rounded-full font-medium hover:bg-gray-100 transition-colors">
                                    查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6">
                        <h3 class="text-xl font-bold mb-2">现代化电商平台</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            基于React和Node.js构建的全栈电商平台，支持用户注册、商品管理、购物车、支付等功能。
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span
                                class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full">React</span>
                            <span
                                class="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm rounded-full">Node.js</span>
                            <span
                                class="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-sm rounded-full">MongoDB</span>
                        </div>
                    </div>
                </div>

                <!-- Project 2 -->
                <div class="group">
                    <div class="relative overflow-hidden rounded-xl shadow-lg">
                        <div class="h-64 bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                            <div class="text-white text-center">
                                <div class="text-6xl mb-4">📊</div>
                                <h3 class="text-2xl font-bold">数据可视化</h3>
                            </div>
                        </div>
                        <div
                            class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <button
                                    class="px-6 py-3 bg-white text-gray-900 rounded-full font-medium hover:bg-gray-100 transition-colors">
                                    查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6">
                        <h3 class="text-xl font-bold mb-2">实时数据仪表板</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            使用Vue.js和D3.js构建的实时数据可视化平台，支持多种图表类型和实时数据更新。
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span
                                class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full">Vue.js</span>
                            <span
                                class="px-3 py-1 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-sm rounded-full">D3.js</span>
                            <span
                                class="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-sm rounded-full">WebSocket</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-4xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">联系我</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">让我们一起创造美好的数字世界</p>
            </div>

            <div class="grid md:grid-cols-2 gap-12">
                <div class="space-y-8">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-bold">邮箱</h3>
                            <p class="text-gray-600 dark:text-gray-300"><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-bold">地址</h3>
                            <p class="text-gray-600 dark:text-gray-300">中国，北京</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-bold">电话</h3>
                            <p class="text-gray-600 dark:text-gray-300">+86 138 0000 0000</p>
                        </div>
                    </div>
                </div>

                <form class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">姓名</label>
                        <input type="text"
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700"
                            placeholder="请输入您的姓名">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">邮箱</label>
                        <input type="email"
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700"
                            placeholder="请输入您的邮箱">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">消息</label>
                        <textarea rows="4"
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700"
                            placeholder="请输入您的消息"></textarea>
                    </div>
                    <button type="submit"
                        class="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                        发送消息
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-6xl mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div
                            class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">F</span>
                        </div>
                        <span class="font-bold text-xl">前端开发者</span>
                    </div>
                    <p class="text-gray-400">
                        用技术创造价值，用代码改变世界。
                    </p>
                </div>

                <div>
                    <h3 class="font-bold mb-4">快速链接</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#home" class="hover:text-white transition-colors">首页</a></li>
                        <li><a href="#about" class="hover:text-white transition-colors">关于我</a></li>
                        <li><a href="#blog" class="hover:text-white transition-colors">博客</a></li>
                        <li><a href="#projects" class="hover:text-white transition-colors">项目</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold mb-4">技术栈</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li>React & Vue.js</li>
                        <li>TypeScript</li>
                        <li>Node.js</li>
                        <li>Tailwind CSS</li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold mb-4">社交媒体</h3>
                    <div class="flex space-x-4">
                        <a href="#"
                            class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                            </svg>
                        </a>
                        <a href="#"
                            class="w-10 h-10 bg-blue-800 rounded-lg flex items-center justify-center hover:bg-blue-900 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                            </svg>
                        </a>
                        <a href="#"
                            class="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 前端开发者. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;

        // Check for saved theme preference or default to light mode
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            html.classList.add('dark');
        } else {
            html.classList.remove('dark');
        }

        themeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            localStorage.theme = html.classList.contains('dark') ? 'dark' : 'light';
        });

        // Mobile Menu
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        const closeMenu = document.getElementById('close-menu');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.remove('hidden');
        });

        closeMenu.addEventListener('click', () => {
            mobileMenu.classList.add('hidden');
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // Close mobile menu if open
                mobileMenu.classList.add('hidden');
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        </script>
        </body>
        </html>