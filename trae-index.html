<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>租易通 - 专业房屋租赁管理系统</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    
    <!-- Tailwind 配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af', // 深蓝主色调
                        secondary: '#3b82f6', // 中蓝辅助色
                        accent: '#60a5fa', // 浅蓝色强调
                        dark: '#0f172a', // 深色背景
                        light: '#f1f5f9', // 浅色背景
                    },
                    fontFamily: {
                        sans: ['system-ui', 'sans-serif'],
                    },
                },
            }
        }
    </script>
    
    <!-- 自定义样式 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .bg-gradient-blue {
                background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            }
            .transition-transform-slow {
                transition: transform 0.5s ease-in-out;
            }
        }
    </style>
</head>
<body class="font-sans bg-light text-dark">
    <!-- 导航栏 -->
    <header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <a href="#" class="flex items-center space-x-2">
                    <div class="w-10 h-10 rounded-full bg-gradient-blue flex items-center justify-center">
                        <i class="fa fa-home text-white text-xl"></i>
                    </div>
                    <span class="text-xl font-bold text-primary">租易通</span>
                </a>
                
                <!-- 桌面端菜单 -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#hero" class="text-dark hover:text-primary transition-colors">首页</a>
                    <a href="#services" class="text-dark hover:text-primary transition-colors">服务</a>
                    <a href="#features" class="text-dark hover:text-primary transition-colors">产品特点</a>
                    <a href="#testimonials" class="text-dark hover:text-primary transition-colors">客户评价</a>
                    <a href="#pricing" class="text-dark hover:text-primary transition-colors">价格</a>
                    <a href="#contact" class="text-dark hover:text-primary transition-colors">联系我们</a>
                </nav>
                
                <!-- 登录/注册按钮 -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="#" class="px-4 py-2 rounded-full border border-primary text-primary hover:bg-primary/5 transition-colors">登录</a>
                    <a href="#" class="px-4 py-2 rounded-full bg-primary text-white hover:bg-primary/90 transition-colors">免费试用</a>
                </div>
                
                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="md:hidden text-dark hover:text-primary transition-colors">
                    <i class="fa fa-bars text-xl"></i>
                </button>
            </div>
            
            <!-- 移动端菜单 -->
            <div id="mobile-menu" class="hidden md:hidden mt-4 py-4 border-t border-gray-100">
                <nav class="flex flex-col space-y-4">
                    <a href="#hero" class="text-dark hover:text-primary transition-colors">首页</a>
                    <a href="#services" class="text-dark hover:text-primary transition-colors">服务</a>
                    <a href="#features" class="text-dark hover:text-primary transition-colors">产品特点</a>
                    <a href="#testimonials" class="text-dark hover:text-primary transition-colors">客户评价</a>
                    <a href="#pricing" class="text-dark hover:text-primary transition-colors">价格</a>
                    <a href="#contact" class="text-dark hover:text-primary transition-colors">联系我们</a>
                    <div class="flex space-x-4 pt-2">
                        <a href="#" class="px-4 py-2 rounded-full border border-primary text-primary hover:bg-primary/5 transition-colors text-center flex-1">登录</a>
                        <a href="#" class="px-4 py-2 rounded-full bg-primary text-white hover:bg-primary/90 transition-colors text-center flex-1">免费试用</a>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- 主横幅区域 -->
    <section id="hero" class="pt-28 pb-16 md:pt-40 md:pb-24 bg-gradient-to-b from-blue-50 to-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight text-dark">
                        智慧租房管理系统<br>
                        <span class="text-primary">让租赁更简单</span>
                    </h1>
                    <p class="mt-6 text-lg text-gray-600 max-w-lg">
                        租易通为房屋租赁企业提供一站式管理解决方案，从房源发布到租金收取，从租户管理到数据分析，全方位提升您的运营效率。
                    </p>
                    <div class="mt-10 flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <a href="#demo" class="px-8 py-3 rounded-full bg-primary text-white text-center hover:bg-primary/90 transition-all transform hover:-translate-y-1 shadow-lg hover:shadow-xl">
                            申请演示
                        </a>
                        <a href="#how-it-works" class="px-8 py-3 rounded-full border border-gray-300 text-dark text-center hover:border-primary hover:text-primary transition-all flex items-center justify-center">
                            <i class="fa fa-play-circle mr-2"></i> 了解更多
                        </a>
                    </div>
                    <div class="mt-10 flex items-center space-x-6">
                        <div class="flex -space-x-2">
                            <img src="https://picsum.photos/seed/user1/40/40" alt="用户头像" class="w-10 h-10 rounded-full border-2 border-white">
                            <img src="https://picsum.photos/seed/user2/40/40" alt="用户头像" class="w-10 h-10 rounded-full border-2 border-white">
                            <img src="https://picsum.photos/seed/user3/40/40" alt="用户头像" class="w-10 h-10 rounded-full border-2 border-white">
                            <div class="w-10 h-10 rounded-full border-2 border-white bg-blue-100 flex items-center justify-center text-primary text-sm font-medium">
                                200+
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center mb-1">
                                <div class="flex text-yellow-400">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-half-o"></i>
                                </div>
                                <span class="ml-2 text-dark font-medium">4.8/5</span>
                            </div>
                            <p class="text-sm text-gray-500">来自200+客户的评价</p>
                        </div>
                    </div>
                </div>
                <div class="md:w-1/2 relative">
                    <div class="relative z-10 rounded-xl overflow-hidden shadow-2xl transform md:translate-x-10 transition-transform-slow hover:scale-[1.02]">
                        <img src="https://picsum.photos/seed/realestate/800/600" alt="房屋租赁管理系统界面" class="w-full h-auto">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                            <div class="p-6 text-white">
                                <h3 class="text-xl font-bold">智能房源管理系统</h3>
                                <p class="mt-2">全面掌控您的租赁业务</p>
                            </div>
                        </div>
                    </div>
                    <!-- 装饰元素 -->
                    <div class="absolute -z-10 top-10 -left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl"></div>
                    <div class="absolute -z-10 bottom-10 -right-10 w-80 h-80 bg-accent/20 rounded-full blur-3xl"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- 合作伙伴 -->
    <section class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <p class="text-center text-gray-500 mb-8">值得信赖的合作伙伴</p>
            <div class="flex flex-wrap justify-center items-center gap-8 md:gap-16">
                <div class="text-gray-400 text-2xl font-bold">链家</div>
                <div class="text-gray-400 text-2xl font-bold">贝壳</div>
                <div class="text-gray-400 text-2xl font-bold">我爱我家</div>
                <div class="text-gray-400 text-2xl font-bold">中原地产</div>
                <div class="text-gray-400 text-2xl font-bold">麦田房产</div>
            </div>
        </div>
    </section>

    <!-- 服务区域 -->
    <section id="services" class="py-20 bg-blue-50">
        <div class="container mx-auto px-4">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark">专业房屋租赁解决方案</h2>
                <p class="mt-4 text-lg text-gray-600">我们的系统为您提供全方位的租赁管理服务，让您的业务更加高效、智能、安全。</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 服务卡片 1 -->
                <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                    <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl mb-6">
                        <i class="fa fa-building-o"></i>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">房源管理</h3>
                    <p class="text-gray-600 mb-6">高效管理房源信息，包括基本信息、图片、设施、价格等，支持批量操作和快速搜索。</p>
                    <a href="#" class="text-primary hover:text-primary/80 inline-flex items-center font-medium">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
                
                <!-- 服务卡片 2 -->
                <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                    <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl mb-6">
                        <i class="fa fa-users"></i>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">租户管理</h3>
                    <p class="text-gray-600 mb-6">完整记录租户信息、租赁合同、付款记录，智能提醒续租、逾期等重要事项。</p>
                    <a href="#" class="text-primary hover:text-primary/80 inline-flex items-center font-medium">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
                
                <!-- 服务卡片 3 -->
                <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                    <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl mb-6">
                        <i class="fa fa-credit-card"></i>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">财务管理</h3>
                    <p class="text-gray-600 mb-6">自动生成财务报表，支持多种付款方式，轻松管理租金、押金、水电费等各项收支。</p>
                    <a href="#" class="text-primary hover:text-primary/80 inline-flex items-center font-medium">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
                
                <!-- 服务卡片 4 -->
                <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                    <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl mb-6">
                        <i class="fa fa-calendar-check-o"></i>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">维修管理</h3>
                    <p class="text-gray-600 mb-6">线上报修、派工、跟踪维修进度，提高维修效率，提升租户满意度。</p>
                    <a href="#" class="text-primary hover:text-primary/80 inline-flex items-center font-medium">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
                
                <!-- 服务卡片 5 -->
                <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                    <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl mb-6">
                        <i class="fa fa-line-chart"></i>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">数据分析</h3>
                    <p class="text-gray-600 mb-6">多维度数据统计和分析，帮助您洞察业务趋势，优化运营策略，提升盈利能力。</p>
                    <a href="#" class="text-primary hover:text-primary/80 inline-flex items-center font-medium">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
                
                <!-- 服务卡片 6 -->
                <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                    <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl mb-6">
                        <i class="fa fa-mobile"></i>
                    </div>
                    <h3 class="text-xl font-bold text-dark mb-4">移动应用</h3>
                    <p class="text-gray-600 mb-6">随时随地管理业务，支持手机、平板等多种设备，让您的工作更加灵活高效。</p>
                    <a href="#" class="text-primary hover:text-primary/80 inline-flex items-center font-medium">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 数据统计 -->
    <section class="py-16 bg-primary text-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div class="">
                    <div class="text-4xl md:text-5xl font-bold mb-2">1000+</div>
                    <p class="text-blue-100">合作企业</p>
                </div>
                <div class="">
                    <div class="text-4xl md:text-5xl font-bold mb-2">50000+</div>
                    <p class="text-blue-100">管理房源</p>
                </div>
                <div class="">
                    <div class="text-4xl md:text-5xl font-bold mb-2">98%</div>
                    <p class="text-blue-100">客户满意度</p>
                </div>
                <div class="">
                    <div class="text-4xl md:text-5xl font-bold mb-2">7年+</div>
                    <p class="text-blue-100">行业经验</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 产品特点 -->
    <section id="features" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark">为什么选择租易通</h2>
                <p class="mt-4 text-lg text-gray-600">我们的系统融合了最新技术和行业最佳实践，为您提供卓越的用户体验和强大的功能。</p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div class="order-2 lg:order-1">
                    <!-- 特点列表 -->
                    <div class="space-y-8">
                        <div class="flex items-start">
                            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl shrink-0 mt-1">
                                <i class="fa fa-bolt"></i>
                            </div>
                            <div class="ml-6">
                                <h3 class="text-xl font-bold text-dark mb-2">高效管理</h3>
                                <p class="text-gray-600">自动化工作流程，减少人工操作，提高工作效率，降低运营成本。</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl shrink-0 mt-1">
                                <i class="fa fa-shield"></i>
                            </div>
                            <div class="ml-6">
                                <h3 class="text-xl font-bold text-dark mb-2">数据安全</h3>
                                <p class="text-gray-600">多重安全防护，数据加密存储，定期备份，保障您的业务数据安全。</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl shrink-0 mt-1">
                                <i class="fa fa-paint-brush"></i>
                            </div>
                            <div class="ml-6">
                                <h3 class="text-xl font-bold text-dark mb-2">界面美观</h3>
                                <p class="text-gray-600">现代化的UI设计，简洁直观的操作界面，提升用户体验和工作效率。</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl shrink-0 mt-1">
                                <i class="fa fa-cogs"></i>
                            </div>
                            <div class="ml-6">
                                <h3 class="text-xl font-bold text-dark mb-2">灵活定制</h3>
                                <p class="text-gray-600">根据您的业务需求，灵活配置系统功能，满足不同规模企业的个性化需求。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="order-1 lg:order-2 relative">
                    <div class="relative z-10 rounded-xl overflow-hidden shadow-2xl transition-transform-slow hover:scale-[1.02]">
                        <img src="https://picsum.photos/seed/dashboard/800/600" alt="租易通系统仪表盘" class="w-full h-auto">
                    </div>
                    <!-- 装饰元素 -->
                    <div class="absolute -z-10 top-10 -left-10 w-64 h-64 bg-secondary/10 rounded-full blur-3xl"></div>
                    <div class="absolute -z-10 bottom-10 -right-10 w-80 h-80 bg-accent/20 rounded-full blur-3xl"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- 数据可视化 -->
    <section class="py-20 bg-blue-50">
        <div class="container mx-auto px-4">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark">智能数据分析</h2>
                <p class="mt-4 text-lg text-gray-600">通过直观的数据可视化，帮助您深入了解业务状况，做出明智决策。</p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl p-6 shadow-lg">
                    <h3 class="text-xl font-bold text-dark mb-4">租金收入趋势</h3>
                    <div class="h-80">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl p-6 shadow-lg">
                    <h3 class="text-xl font-bold text-dark mb-4">房源分布情况</h3>
                    <div class="h-80">
                        <canvas id="propertyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 客户评价 -->
    <section id="testimonials" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark">客户怎么说</h2>
                <p class="mt-4 text-lg text-gray-600">来自全国各地的房屋租赁企业对我们的系统给予了高度评价。</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 评价卡片 1 -->
                <div class="bg-blue-50 rounded-xl p-8 hover:shadow-lg transition-all">
                    <div class="flex text-yellow-400 mb-4">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                    </div>
                    <p class="text-gray-700 mb-6 italic">"租易通系统帮助我们将房源管理效率提升了50%以上，租户满意度显著提高，是我们业务增长的得力助手。"</p>
                    <div class="flex items-center">
                        <img src="https://picsum.photos/seed/client1/60/60" alt="客户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-dark">张明</h4>
                            <p class="text-sm text-gray-500">链家地产区域经理</p>
                        </div>
                    </div>
                </div>
                
                <!-- 评价卡片 2 -->
                <div class="bg-blue-50 rounded-xl p-8 hover:shadow-lg transition-all">
                    <div class="flex text-yellow-400 mb-4">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                    </div>
                    <p class="text-gray-700 mb-6 italic">"系统的数据分析功能非常强大，帮助我们准确把握市场趋势，优化房源定价，提高了出租率和收益。"</p>
                    <div class="flex items-center">
                        <img src="https://picsum.photos/seed/client2/60/60" alt="客户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-dark">李婷</h4>
                            <p class="text-sm text-gray-500">我爱我家运营总监</p>
                        </div>
                    </div>
                </div>
                
                <!-- 评价卡片 3 -->
                <div class="bg-blue-50 rounded-xl p-8 hover:shadow-lg transition-all">
                    <div class="flex text-yellow-400 mb-4">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star-half-o"></i>
                    </div>
                    <p class="text-gray-700 mb-6 italic">"租易通的客服团队非常专业，系统上线过程顺利，培训到位，后续支持及时响应，让我们无后顾之忧。"</p>
                    <div class="flex items-center">
                        <img src="https://picsum.photos/seed/client3/60/60" alt="客户头像" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-dark">王强</h4>
                            <p class="text-sm text-gray-500">中原地产技术负责人</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 价格方案 -->
    <section id="pricing" class="py-20 bg-blue-50">
        <div class="container mx-auto px-4">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark">选择适合您的方案</h2>
                <p class="mt-4 text-lg text-gray-600">我们提供多种灵活的价格方案，满足不同规模企业的需求。</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <!-- 基础版 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                    <div class="p-8">
                        <h3 class="text-xl font-bold text-dark mb-2">基础版</h3>
                        <p class="text-gray-600 mb-6">适合初创型租赁企业</p>
                        <div class="flex items-baseline mb-6">
                            <span class="text-4xl font-bold text-dark">¥999</span>
                            <span class="text-gray-500 ml-2">/月</span>
                        </div>
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">管理50套以内房源</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">基础房源管理功能</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">租户信息管理</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">基础财务报表</span>
                            </li>
                            <li class="flex items-center text-gray-400">
                                <i class="fa fa-times mr-3"></i>
                                <span>高级数据分析</span>
                            </li>
                            <li class="flex items-center text-gray-400">
                                <i class="fa fa-times mr-3"></i>
                                <span>自定义报表</span>
                            </li>
                        </ul>
                        <a href="#contact" class="block w-full py-3 px-6 rounded-full border border-primary text-primary text-center hover:bg-primary/5 transition-colors font-medium">
                            立即选购
                        </a>
                    </div>
                </div>
                
                <!-- 企业版 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-xl border-2 border-primary transform md:scale-105 transition-all transform hover:-translate-y-1 relative">
                    <div class="absolute top-0 right-0 bg-primary text-white text-sm font-medium px-4 py-1">
                        最受欢迎
                    </div>
                    <div class="p-8">
                        <h3 class="text-xl font-bold text-dark mb-2">企业版</h3>
                        <p class="text-gray-600 mb-6">适合中大型租赁企业</p>
                        <div class="flex items-baseline mb-6">
                            <span class="text-4xl font-bold text-dark">¥2999</span>
                            <span class="text-gray-500 ml-2">/月</span>
                        </div>
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">管理500套以内房源</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">全部房源管理功能</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">租户信息全管理</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">完整财务报表</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">高级数据分析</span>
                            </li>
                            <li class="flex items-center text-gray-400">
                                <i class="fa fa-times mr-3"></i>
                                <span>自定义报表</span>
                            </li>
                        </ul>
                        <a href="#contact" class="block w-full py-3 px-6 rounded-full bg-primary text-white text-center hover:bg-primary/90 transition-colors font-medium">
                            立即选购
                        </a>
                    </div>
                </div>
                
                <!-- 定制版 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                    <div class="p-8">
                        <h3 class="text-xl font-bold text-dark mb-2">定制版</h3>
                        <p class="text-gray-600 mb-6">适合大型连锁企业</p>
                        <div class="flex items-baseline mb-6">
                            <span class="text-4xl font-bold text-dark">¥9999</span>
                            <span class="text-gray-500 ml-2">/月</span>
                        </div>
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">无限房源管理</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">全部功能</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">多门店管理</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">高级权限控制</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">高级数据分析</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-green-500 mr-3"></i>
                                <span class="text-gray-600">自定义报表</span>
                            </li>
                        </ul>
                        <a href="#contact" class="block w-full py-3 px-6 rounded-full border border-primary text-primary text-center hover:bg-primary/5 transition-colors font-medium">
                            联系销售
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section id="contact" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
                <div>
                    <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-6">联系我们</h2>
                    <p class="text-lg text-gray-600 mb-10">如果您有任何问题或需求，请随时与我们联系，我们的专业团队将为您提供最优质的服务。</p>
                    
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl shrink-0 mt-1">
                                <i class="fa fa-map-marker"></i>
                            </div>
                            <div class="ml-6">
                                <h3 class="text-lg font-bold text-dark mb-1">公司地址</h3>
                                <p class="text-gray-600">北京市朝阳区建国路88号现代城8层</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl shrink-0 mt-1">
                                <i class="fa fa-phone"></i>
                            </div>
                            <div class="ml-6">
                                <h3 class="text-lg font-bold text-dark mb-1">联系电话</h3>
                                <p class="text-gray-600">************</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl shrink-0 mt-1">
                                <i class="fa fa-envelope-o"></i>
                            </div>
                            <div class="ml-6">
                                <h3 class="text-lg font-bold text-dark mb-1">电子邮箱</h3>
                                <p class="text-gray-600"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-10">
                        <h3 class="text-lg font-bold text-dark mb-4">关注我们</h3>
                        <div class="flex space-x-4">
                            <a href="#" class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                                <i class="fa fa-weixin"></i>
                            </a>
                            <a href="#" class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                                <i class="fa fa-weibo"></i>
                            </a>
                            <a href="#" class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-colors">
                                <i class="fa fa-linkedin"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 rounded-xl p-8">
                    <h3 class="text-xl font-bold text-dark mb-6">发送消息</h3>
                    <form>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="name" class="block text-gray-700 font-medium mb-2">姓名</label>
                                <input type="text" id="name" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors" placeholder="请输入您的姓名">
                            </div>
                            <div>
                                <label for="email" class="block text-gray-700 font-medium mb-2">邮箱</label>
                                <input type="email" id="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors" placeholder="请输入您的邮箱">
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label for="company" class="block text-gray-700 font-medium mb-2">公司名称</label>
                            <input type="text" id="company" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors" placeholder="请输入您的公司名称">
                        </div>
                        
                        <div class="mb-6">
                            <label for="message" class="block text-gray-700 font-medium mb-2">留言内容</label>
                            <textarea id="message" rows="5" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors" placeholder="请输入您的留言内容"></textarea>
                        </div>
                        
                        <button type="submit" class="w-full py-3 px-6 rounded-lg bg-primary text-white text-center hover:bg-primary/90 transition-colors font-medium">
                            提交留言
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white pt-16 pb-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12">
                <div>
                    <div class="flex items-center space-x-2 mb-6">
                        <div class="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                            <i class="fa fa-home text-primary text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-white">租易通</span>
                    </div>
                    <p class="text-gray-400 mb-6">专业的房屋租赁管理系统，为租赁企业提供全方位的解决方案。</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-weixin"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-weibo"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-linkedin"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-bold text-white mb-6">产品</h4>
                    <ul class="space-y-4">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">房源管理</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">租户管理</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">财务管理</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">维修管理</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">数据分析</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-bold text-white mb-6">公司</h4>
                    <ul class="space-y-4">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">关于我们</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">客户案例</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">合作伙伴</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">新闻动态</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">加入我们</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-bold text-white mb-6">支持</h4>
                    <ul class="space-y-4">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">帮助中心</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">使用文档</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">常见问题</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">联系我们</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">服务条款</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2023 租易通. 保留所有权利。</p>
                    <div class="flex space-x-6">
                        <a href="#" class="text-gray-400 text-sm hover:text-white transition-colors">隐私政策</a>
                        <a href="#" class="text-gray-400 text-sm hover:text-white transition-colors">服务条款</a>
                        <a href="#" class="text-gray-400 text-sm hover:text-white transition-colors">Cookie 政策</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 导航栏滚动效果
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                navbar.classList.add('py-2', 'shadow-md');
                navbar.classList.remove('py-3', 'shadow-sm');
            } else {
                navbar.classList.add('py-3', 'shadow-sm');
                navbar.classList.remove('py-2', 'shadow-md');
            }
        });
        
        // 移动端菜单
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
            if (mobileMenuBtn.querySelector('i').classList.contains('fa-bars')) {
                mobileMenuBtn.querySelector('i').classList.remove('fa-bars');
                mobileMenuBtn.querySelector('i').classList.add('fa-times');
            } else {
                mobileMenuBtn.querySelector('i').classList.remove('fa-times');
                mobileMenuBtn.querySelector('i').classList.add('fa-bars');
            }
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    // 关闭移动端菜单
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                        mobileMenuBtn.querySelector('i').classList.remove('fa-times');
                        mobileMenuBtn.querySelector('i').classList.add('fa-bars');
                    }
                }
            });
        });
        
        // 数据可视化 - 租金收入趋势图表
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                datasets: [{
                    label: '租金收入',
                    data: [12000, 19000, 15000, 20000, 25000, 22000, 28000, 30000, 26000, 32000, 35000, 40000],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true,
                    tension: 0.4,
                    borderWidth: 2,
                    pointBackgroundColor: '#1e40af',
                    pointRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + value;
                            }
                        }
                    }
                }
            }
        });
        
        // 数据可视化 - 房源分布图表
        const propertyCtx = document.getElementById('propertyChart').getContext('2d');
        const propertyChart = new Chart(propertyCtx, {
            type: 'doughnut',
            data: {
                labels: ['一室一厅', '两室一厅', '三室一厅', '三室两厅', '四室及以上'],
                datasets: [{
                    data: [30, 25, 20, 15, 10],
                    backgroundColor: [
                        '#1e40af',
                        '#3b82f6',
                        '#60a5fa',
                        '#93c5fd',
                        '#bfdbfe'
                    ],
                    borderWidth: 0,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                },
                cutout: '60%'
            }
        });
    </script>
</body>
</html>